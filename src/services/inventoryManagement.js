/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from "../utils/request";

// 库存记录，获取列表
export async function getList(params) {
  return request("/Api/YZ/CourierStation/getStockList", {
    method: "POST",
    body: params,
  });
}
// 库存记录，创建导出任务
export async function createExport(params) {
  return request("/Api/YZ/CourierStation/export", {
    method: "POST",
    body: params,
  });
}
