/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

@import '~antd/es/style/themes/default.less';
@import "./antd4.less";

@font-face {
  font-weight: 400;
  // 勿删
  font-family: swiper-icons;
  font-style: normal;
  src: url('data:application/font-woff;charset=utf-8;base64, 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')
    format('woff');
}

@border-base : @border-width-base @border-style-base @normal-color;
html,
body,
#root {
  height: 100%;
}

//图片预览弹窗
.preview-image-modal {
  .ant-modal-close {
    position: fixed;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 0 0 0 50%;
  }
  .ant-modal-close-x::before {
    font-size: 25px;
  }
  .ant-modal-body {
    position: relative;
    z-index: 11;
    padding: 0;
    background-color: #0000;
    box-shadow: none;
  }
  .ant-modal {
    width: auto !important;
    height: auto;
  }
  .ant-modal-content {
    background-color: transparent;
    box-shadow: none;
  }
}

.ant-layout {
  min-height: 100%;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// braft-icons请勿删除
body * {
  font-family: Microsoft YaHei, Chinese Quote, -apple-system, BlinkMacSystemFont, Segoe UI,
    PingFang SC, Hiragino Sans GB, Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji,
    Segoe UI Emoji, Segoe UI Symbol, braft-icons;
}

// temp fix for https://github.com/ant-design/ant-design/commit/a1fafb5b727b62cb0be29ce6e9eca8f579d4f8b7
.ant-spin-container {
  overflow: visible !important;
}

.global-spin {
  width: 100%;
  margin: 40px 0 !important;
}

//弹窗按钮
.ant-modal-footer > div {
  button:nth-child(1) {
    float: right;
    margin-left: 8px;
  }
}

.ant-table-fixed-right {
  z-index: 1;
}

.full-screen {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
  z-index: 999;
  box-sizing: border-box !important;
  width: 100% !important;
  min-width: 0 !important;
  max-width: none !important;
  height: 100% !important;
  min-height: 0 !important;
  max-height: none !important;
  margin: 0 !important;
  object-fit: contain;
  transform: none !important;
}

.ant-popover .ant-popover-arrow {
  background-color: #fff;
}

.ant-menu-inline .ant-menu-item,
.ant-menu-inline .ant-menu-submenu-title {
  width: 100% !important;
}

.progress {
  .ant-modal-content {
    background: none !important;
    box-shadow: none !important;
  }
  .ant-progress-circle .ant-progress-text {
    color: #fff;
  }
}

.kb-list {
  overflow: hidden;
  border-radius: @border-radius-base;
  .ant-list-item {
    justify-content: flex-start;
  }
  &.kb-list-select {
    .ant-list-item {
      cursor: pointer;
    }
  }
  &.ant-list-bordered .ant-list-item,
  .ant-list-item {
    padding: @padding-lg / 2;
    line-height: 40px;
    transition: all @animation-duration-slow;
    & > span {
      flex-grow: 1;
    }
    &:hover {
      background-color: @table-row-hover-bg;
    }
    & > .ant-row {
      box-sizing: border-box;
      width: 100%;
    }
  }
  .ant-list-item-action {
    line-height: 1;
    & > li {
      line-height: 40px;
    }
    & > li:last-child {
      padding-right: 0;
    }
  }
  .ant-list-header {
    padding: @padding-md / 2 @padding-md;
    color: #666;
    background-color: #f2f2f2;
  }
  & &_disabled {
    cursor: default !important;
    opacity: 0.5;
    &:hover {
      background-color: transparent;
    }
  }
}

.kb-tips {
  text-align: left;
  &_title {
    margin-bottom: @padding-md / 2;
  }
  &_item {
    margin-bottom: @padding-md / 2;
    padding: 0 40px 0 16px;
    overflow: hidden;
    color: #999;
    &:last-child {
      margin-bottom: 0;
    }
    &::before {
      display: inline-block;
      margin-right: 8px;
      margin-left: -20px;
      border: 6px solid #d9d9d9;
      border-radius: 4px;
      transform: rotate(45deg) translateY(2px);
      content: '';
    }
  }
}

.kb-radio-group {
  line-height: unset !important;
  & > button {
    margin-right: @padding-md;
    &:last-child {
      margin-right: 0;
    }
  }
  .ant-radio-button-wrapper {
    margin-right: @padding-md;
    border: @border-base;
    border-radius: @border-radius-base !important;
    &:not(:first-child)::before {
      display: none !important;
    }
    &-checked:not(.ant-radio-button-wrapper-disabled) {
      border-color: @primary-color !important;
      box-shadow: none !important;
    }
  }
}

.grecaptcha-badge {
  visibility: hidden;
}

@media screen and (max-width: @screen-sm) {
  .ant-modal-centered .ant-modal {
    width: 100% !important;
    .ant-modal-body {
      padding: 12px;
    }
  }
}
.ant-card-head-title {
  white-space: pre-wrap !important;
}

.listItem {
  border-radius: 8px;
  background-color: #fff;
  margin: 8px;
  margin-top: 0;
  padding: 8px;
  cursor: pointer;
  &-title {
    color: #333;
    font-weight: bold;
    font-size: 15px;
  }
}

.kb-authorized-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.truck-step {
  margin-top: 12px !important;
  :global(.ant-steps-item-container) {
    text-align: left;
  }
}

.amap_markers_pop_window {
  max-height: 200px;
  overflow: auto;
}
