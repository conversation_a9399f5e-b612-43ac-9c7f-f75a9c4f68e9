/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable no-shadow */
import React, { useState, useMemo, useRef } from 'react';
import { useSelector } from 'dva';
import { useDebounceFn } from 'ahooks';
import { Form, Input, Button, message, Modal } from 'antd';
import { deleteMixin, getMixInList } from '@/services/setup';
import MixInModal from '@/components/_pages/Post/Setup/MixInStorage/MixInModal';
import UploadWithCrypto from '@/components/upload-width-crypto';
import ProTableExtend from '@/components/ProTableExtend';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

const FormItem = Form.Item;

const MixInStorage = props => {
  const { user = {}, setting = {}, area = {} } = useSelector(state => state);
  const { area_id } = area;
  const { currentUser: { quickLoginType } = {} } = user || {};
  const { form } = props;
  const { getFieldDecorator, validateFields } = form;
  const isZyAccount = setting.options.key == 'post';
  const [visible, setVisible] = useState(false);
  const [cm_id, setCmId] = useState('');
  const [uploading, setUploading] = useState(false);
  const [isNew, setIsNew] = useState(false);
  const [isCopy, setIsCopy] = useState(false);
  const actionRef = useRef();

  const getTableList = async params => {
    const { current, pageSize = 20, ...rest } = params;
    const { list, total, page } = await getMixInList({
      page: current,
      pageSize,
      area_id,
      isZyAccount,
      ...rest,
    });
    return {
      data: Array.isArray(list) ? list : [],
      total,
      current: page,
    };
  };

  const { run: onSubmit } = useDebounceFn(
    e => {
      e?.preventDefault?.();
      validateFields(async err => {
        if (err) return;
        actionRef.current.submit();
      });
    },
    {
      wait: 300,
      leading: true,
      trailing: false,
    },
  );

  const handelCheckDetail = ({ cm_id, isNew } = {}) => {
    setCmId(cm_id);
    setVisible(true);
    setIsNew(!!isNew);
    setIsCopy(false);
  };

  // 复制功能处理函数
  const handleCopy = async data => {
    try {
      // // 获取详情数据（新）
      // const detailData = await getMixInDetail({ cm_id: data.cm_id, isZyAccount, isNew: 1 });
      if (data.cm_id) {
        // 打开添加弹窗，内容为查看详情（新）的数据
        setCmId(data.cm_id); // 传入原始cm_id用于获取数据
        setVisible(true);
        setIsNew(true); // 使用新版本的表单
        setIsCopy(true); // 标记为复制模式
      }
    } catch (error) {
      message.error('获取详情数据失败');
    }
  };

  const handleDelete = data => {
    Modal.confirm({
      title: '温馨提示',
      content: (
        <span style={{ color: 'red', fontSize: 24 }}>
          该驿站代入库设置将被删除，不可恢复！！！是否继续？
        </span>
      ),
      okText: '继续',
      cancelText: '取消',
      onOk: async () => {
        const { code, msg } = await deleteMixin({ cm_id: data.cm_id, isZyAccount });
        if (code == 0) {
          message.success('删除成功');
          actionRef.current.submit();
        } else {
          message.error(msg);
        }
      },
    });
  };

  const columns = useMemo(
    () => {
      const arr = [
        {
          width: 100,
          align: 'center',
          title: 'id',
          dataIndex: 'cm_id',
        },
        {
          width: 200,
          align: 'center',
          title: '驿站名称',
          dataIndex: 'inn_name',
        },
        {
          width: 150,
          align: 'center',
          title: '驿站账号',
          dataIndex: 'phone',
        },
        {
          width: 150,
          align: 'center',
          title: '入库品牌',
          dataIndex: 'platform',
        },
        {
          width: 200,
          align: 'center',
          title: '更新时间',
          dataIndex: 'update_time',
        },
      ];

      if ([2, 3].includes(quickLoginType)) {
        arr.push({
          width: 200,
          align: 'center',
          title: '详情',
          render: (_, data) => (
            <span>
              {data.is_rules_new == 1 ? (
                <a onClick={handelCheckDetail.bind(null, { ...data, isNew: 1 })}>查看详情（新）</a>
              ) : (
                <>
                  <a onClick={handelCheckDetail.bind(null, data)}>查看详情</a>
                  <a
                    style={{ marginLeft: '18px' }}
                    onClick={handelCheckDetail.bind(null, { ...data, isNew: 1 })}
                  >
                    查看详情（新）
                  </a>
                </>
              )}
              <a style={{ marginLeft: '18px' }} onClick={() => handleDelete(data)}>
                删除
              </a>
              {data.is_rules_new == 1 && (
                <a style={{ marginLeft: '18px' }} onClick={() => handleCopy(data)}>
                  复制
                </a>
              )}
            </span>
          ),
        });
      }

      return arr;
    },
    [quickLoginType],
  );

  const handleChange = () => {
    actionRef.current.submit();
  };

  const onUpLoadChange = info => {
    setUploading(true);
    if (info.file.status === 'done') {
      setUploading(false);
      if (info.file.response.code != 0) {
        message.error(`${info.file.name} 上传失败：${info.file.response.msg}`);
      } else {
        message.success(`${info.file.name} 上传成功`);
        actionRef.current.submit();
      }
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} 上传失败`);
      setUploading(false);
    }
  };

  // useEffect(() => {
  //   dispatch({
  //     type: 'area/setHandleSearch',
  //     payload: {
  //       handleSearch: () => {
  //         console.log('handleSearch');
  //         onSubmit();
  //       },
  //     },
  //   });
  // }, []);

  const params = useMemo(
    () => ({
      area_id,
      isZyAccount,
    }),
    [area_id, isZyAccount],
  );

  return (
    <div>
      <Form layout="inline" {...formItemLayout} style={{ marginBottom: 24 }}>
        <FormItem>
          {getFieldDecorator('search')(
            <Input allowClear placeholder="驿站手机号/名称" style={{ width: 250 }} />,
          )}
        </FormItem>
        <FormItem>
          <Button type="primary" onClick={onSubmit}>
            查询
          </Button>
        </FormItem>
        {[2, 3].includes(quickLoginType) && (
          <>
            {/* <FormItem>
              <Button
                type="primary"
                onClick={() => {
                  handelCheckDetail();
                }}
              >
                添加
              </Button>
            </FormItem> */}
            <FormItem>
              <Button
                type="primary"
                onClick={() => {
                  handelCheckDetail({ isNew: 1 });
                }}
              >
                添加
              </Button>
            </FormItem>
          </>
        )}
        {quickLoginType == 3 && (
          <FormItem>
            <UploadWithCrypto
              action={
                isZyAccount ? '/Api/ChinaPost/Dak/complateImport' : '/Api/YZ/League/complateImport'
              }
              name="file"
              multiple={false}
              accept=".csv"
              showUploadList={false}
              data=""
              onChange={onUpLoadChange}
            >
              <Button type="primary" icon="upload" loading={uploading}>
                导入
              </Button>
            </UploadWithCrypto>
          </FormItem>
        )}
      </Form>
      <ProTableExtend
        scroll={{ x: 800 }}
        id="cm_id"
        bordered={false}
        columns={columns}
        actionRef={actionRef}
        formRef={form}
        request={getTableList}
        params={params}
        cache
        pagination={{
          showQuickJumper: false,
          showSizeChanger: false,
          showTotal: false,
          customItemRender: false,
        }}
      />
      <MixInModal
        visible={visible}
        onCancel={() => {
          setVisible(false);
          setIsCopy(false); // 重置复制状态
        }}
        onOk={handleChange}
        cm_id={cm_id}
        isNew={isNew}
        isCopy={isCopy}
      />
    </div>
  );
};

export default Form.create()(MixInStorage);
