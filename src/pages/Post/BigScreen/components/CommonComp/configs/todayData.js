/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/**
 * 今日数据配置
 */
export const todayList = [
  {
    key: 1,
    title: '今日总入库量',
    dataKey: 'in_num',
  },
  {
    key: 2,
    title: '今日驿站入库量',
    dataKey: 'in_num',
    isStationOnly: true, // 标记需要减去入柜数
  },
  {
    key: 3,
    title: '今日入柜量',
    dataKey: 'in_cabinet',
  },
  {
    key: 4,
    title: '今日到件量',
    dataKey: 'arrive_num',
  },
  {
    key: 5,
    title: '今日派件量',
    dataKey: 'send_num',
  },
  {
    key: 6,
    title: '今日寄件量',
    dataKey: 'express_num',
  },
  {
    key: 7,
    title: '今日快递费',
    dataKey: 'express_fee',
  },
  {
    key: 8,
    title: '今日出库量',
    dataKey: 'out_num',
  },
  {
    key: 9,
    title: '今日取件量',
    dataKey: 'out_cabinet',
  },
];

// 创建key到配置的映射，方便查找
export const todayConfigMap = todayList.reduce((map, config) => {
  map[config.key] = config;
  return map;
}, {});

// 默认显示的配置（前4项）
export const defaultTodayConfig = [1, 4, 5, 6]; // 入库数、到件量、派件量、寄件量
