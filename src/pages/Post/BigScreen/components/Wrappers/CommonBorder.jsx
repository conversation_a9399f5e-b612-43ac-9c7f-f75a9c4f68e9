/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { useRef, useEffect, useState } from 'react';
import { Row, Col, Spin, Progress, Dropdown, Icon } from 'antd';
import { useRequest, useMount } from 'ahooks';
import classNames from 'classnames';
import PropTypes from 'prop-types';
import styles from './CommonBorder.less';

const CommonBorder = ({
  children,
  title,
  showBg,
  loading,
  delay,
  showProgress,
  requestCallback,
  onSuccess,
  onError,
  showSelect,
  menu,
  hideTitle,
  ...rest
}) => {
  const timerRef = useRef();
  const [percent, setPercent] = useState(99);
  const [countNum, setCountNum] = useState(delay);

  const { loading: reqLoading, run } = useRequest(requestCallback, {
    pollingWhenHidden: false,
    manual: true,
    throwOnError: true,
    onSuccess: res => onSuccess(res),
    onError: err => onError(err),
  });

  useMount(() => {
    delay && run();
  });

  useEffect(
    () => {
      countNum < 0 && run();
    },
    [countNum, run],
  );

  // 当requestCallback改变时，立即触发新的请求
  useEffect(
    () => {
      if (delay) {
        run();
      }
    },
    [requestCallback, run, delay],
  );

  useEffect(
    () => {
      if (delay) {
        if (reqLoading) {
          clearInterval(timerRef.current);
          setCountNum(delay);
          setPercent(99);
        } else {
          timerRef.current = setInterval(() => {
            setCountNum(num => num - 1);
            setPercent(num => num - 99 / delay);
          }, 1000);
        }
      }
      return () => {
        clearInterval(timerRef.current);
      };
    },
    [delay, reqLoading],
  );

  return (
    <div className={styles.outBorder} {...rest}>
      {showProgress ? <Progress percent={percent} format={() => `${countNum}s后刷新`} /> : null}
      <Spin style={{ maxHeight: '100%' }} spinning={loading || reqLoading}>
        <Row
          className={classNames(styles.main, {
            [styles.main_bg]: showBg,
          })}
          type="flex"
          justify="space-between"
          style={{ padding: hideTitle ? '0px' : '15px' }}
        >
          <Col style={{ marginBottom: 5, display: hideTitle ? 'none' : 'block' }}>
            <Row type="flex" justify="start" align="middle" className={styles.title}>
              <Col className={styles.line} />
              <Col style={{ cursor: showSelect ? 'pointer' : 'text' }} className={styles.titleText}>
                {showSelect ? (
                  <Dropdown overlay={menu}>
                    <div>
                      {title} <Icon type="down" />
                    </div>
                  </Dropdown>
                ) : (
                  title
                )}
              </Col>
            </Row>
          </Col>
          <Col style={{ flex: 1 }}>{children}</Col>
        </Row>
      </Spin>
    </div>
  );
};

CommonBorder.defaultProps = {
  menu: null,
  showBg: false,
  showSelect: false,
  loading: false,
  delay: 0, // 轮询间隔（秒）
  showProgress: false, // 是否展示进度条
  onSuccess: () => {}, // 获取请求结果
  onError: () => {}, // 获取请求失败结果
  requestCallback: () =>
    new Promise(resolve => {
      resolve(null);
    }), // 轮询请求的函数（promise）
  hideTitle: false, // 是否展示title
};

CommonBorder.propTypes = {
  title: PropTypes.oneOfType([PropTypes.string, PropTypes.element]).isRequired,
  showBg: PropTypes.bool,
  loading: PropTypes.bool,
  delay: PropTypes.number,
  showProgress: PropTypes.bool,
  showSelect: PropTypes.bool,
  menu: PropTypes.any,
  requestCallback: PropTypes.func,
  onSuccess: PropTypes.func,
  onError: PropTypes.func,
  hideTitle: PropTypes.bool,
};

export default CommonBorder;
