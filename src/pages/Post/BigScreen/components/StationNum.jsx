import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { Chart, Geom, Axis, Tooltip, Legend } from 'bizcharts';
import { useSelector } from 'dva';
import { getStageNum, getStationNum } from '@/services/chartData';
import dayjs from 'dayjs';
import { useInterval } from 'ahooks';
import NoDate from './Wrappers/NoDate';
import CommonBorder from './Wrappers/CommonBorder';
import { gradualTextColumn } from './_utils';
import { getInStorageNum } from '@/services/dashboard';

const StationNum = props => {
  const { isParentFull, type, showCabinet } = props;
  const timer = useRef();
  const heightRef = useRef();
  const [height, setHeight] = useState(0);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const { shop_id } = useSelector(({ global }) => global.screenLoginInfo);
  const { screenToken: token } = useSelector(({ global }) => global);

  // 图表配置
  const cols = useMemo(
    () => ({
      date: {
        type: 'cat',
      },
    }),
    [],
  );

  const label = useMemo(
    () => ({
      textStyle: {
        fill: gradualTextColumn, // 文本的颜色
        fontSize: '14', // 文本大小
        fontWeight: 'bold', // 文本粗细
      },
    }),
    [],
  );

  // 数据格式化函数
  const formatInStorageData = useCallback(({ list, showCabinet: cabinet, label: title }) => {
    const result = [];
    list.forEach(item => {
      if (cabinet) {
        result.push(
          {
            date: item.date,
            type: '驿站',
            value: item.num - item.cabinet,
          },
          {
            date: item.date,
            type: '快递柜',
            value: item.cabinet,
          },
        );
      } else {
        result.push({
          date: item.date,
          type: title,
          value: item.num,
        });
      }
    });
    return result;
  }, []);

  const formatStationData = useCallback(({ list, showCabinet: cabinet }) => {
    const result = [];
    list.forEach(item => {
      if (cabinet) {
        result.push(
          {
            date: item.date,
            type: '入库驿站',
            value: item.in_station_num,
          },
          {
            date: item.date,
            type: '全部驿站',
            value: item.total_station_num,
          },
          {
            date: item.date,
            type: '快递柜',
            value: item.new_cabinet_num,
          },
        );
      } else {
        result.push(
          {
            date: item.date,
            type: '入库驿站',
            value: item.in_station_num,
          },
          {
            date: item.date,
            type: '全部驿站',
            value: item.total_station_num,
          },
        );
      }
    });
    return result;
  }, []);

  // 配置映射
  const config = useMemo(
    () => ({
      in: {
        title: '每日入库量',
        request: getInStorageNum,
        format: formatInStorageData,
        label: '入库量',
      },
      station: {
        title: '驿站数量',
        request: getStationNum,
        format: formatStationData,
      },
      new_station: {
        title: '每日新增站点数',
        request: getStageNum,
        format: formatInStorageData,
        label: '站点数',
      },
    }),
    [formatInStorageData, formatStationData],
  );

  // 获取数据函数
  const fetchData = useCallback(
    () => {
      if (!shop_id || !token) return;
      const currentConfig = config[type];
      if (!currentConfig) return;
      setLoading(true);
      currentConfig
        .request({ shop_id, token })
        .then(res => {
          if (res.code == 0 && Array.isArray(res.data)) {
            setData(
              currentConfig.format({ list: res.data, showCabinet, label: currentConfig.label }),
            );
          }
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [shop_id, token, type, showCabinet, config],
  );

  // 当关键依赖变化时重新获取数据
  useEffect(
    () => {
      fetchData();
    },
    [fetchData],
  );

  // 高度设置逻辑优化
  useEffect(
    () => {
      const updateHeight = () => {
        if (heightRef.current) {
          setHeight(heightRef.current.clientHeight);
        }
      };

      timer.current = setTimeout(updateHeight, 1000);

      return () => {
        if (timer.current) {
          clearTimeout(timer.current);
        }
      };
    },
    [isParentFull],
  );

  // 定时刷新数据
  useInterval(fetchData, 60 * 60 * 1000);

  const hasData = data.length > 0;

  return (
    <CommonBorder title={config[type].title} loading={loading}>
      <div style={{ height: '100%' }} ref={heightRef}>
        {hasData ? (
          <Chart
            height={height}
            data={data}
            scale={cols}
            forceFit
            padding={['auto', 'auto', 'auto', 'auto']}
            style={{ backgroundColor: 'rgba(15, 24, 55, 0.7)' }}
          >
            <Legend position="top-right" />
            <Axis name="date" label={{ ...label, formatter: val => dayjs(val).format('DD') }} />
            <Axis
              name="value"
              label={{
                ...label,
                formatter: val => `${val}`,
              }}
            />
            <Tooltip
              useHtml
              crosshairs={{
                type: 'y',
              }}
            />
            <Geom type="line" position="date*value" size={2} shape="smooth" color="type" />
            <Geom
              type="point"
              position="date*value"
              size={4}
              shape="circle"
              color="type"
              style={{
                stroke: '#fff',
                lineWidth: 1,
              }}
            />
          </Chart>
        ) : (
          <NoDate title="暂无数据" style={{ height }} />
        )}
      </div>
    </CommonBorder>
  );
};

export default StationNum;
