/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { useState, useEffect, useRef } from 'react';
import { Modal, Form, Input, message, Radio, Button, Row, Col } from 'antd';
import { addMixedIn, getMixInDetail } from '@/services/setup';
import { useSelector } from 'dva';
import MixInModalItem from './MixInModalItem';
import SmsModal from './SmsModal';
// import { findDuplicates } from './utils';
import Rule from './rule';
import VerifyModal from './verifyModal';
import HistoryModal from './historyModal';

const FormItem = Form.Item;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

/**
 * 混合入库，添加修改弹窗
 *  */
const MixInModal = props => {
  const { visible, form, onOk, onCancel, cm_id, isNew, isCopy } = props;
  const { key: zyKey } = useSelector(({ setting }) => setting.options);
  const isZyAccount = zyKey === 'post';

  const [submitting, setSubmitting] = useState(false);
  const [modalConfig, setModalConfig] = useState([]);
  const [commonBrands, setCommonBrands] = useState([]);
  // const [originalBrands, setOriginalBrands] = useState({});
  // 需要短信验证的手机号
  const [smsPhone, setSmsPhone] = useState({
    phone: '',
    platform: '',
    password: '',
  });

  const [radioValue, setRadioValue] = useState(0);
  const [changed, setChanged] = useState(false);
  const [id, setId] = useState(null);
  const [parentPhone, setParentPhone] = useState('');

  const ruleRef = useRef(null);
  const verifyModalRef = useRef(null);

  const { getFieldDecorator, validateFields, resetFields, setFieldsValue, isFieldsTouched } = form;

  const handleOk = ({ smsCode } = {}) => {
    if (!isFieldsTouched() && !changed && !isCopy) {
      onCancel();
      return;
    }
    validateFields((err, values) => {
      if (err) return;
      const params = {
        cm_id,
        ecommerce: radioValue,
      };
      const selectBrands = [];
      const data = [];
      // let hasExtend = true;
      Object.keys(values).forEach(key => {
        if (typeof values[key] == 'string') {
          params[key] = values[key];
          return;
        }
        const {
          checked,
          platform,
          username,
          password,
          platform_brand,
          extend,
          assignments,
          ecommerce,
          repeat_brand,
        } = values[key] || {};
        checked && repeat_brand && selectBrands.push(...platform_brand);
        data.push({
          platform,
          username,
          password,
          status: checked ? 1 : 0,
          smsCode: smsCode && smsPhone.platform == platform ? smsCode : '',
          platform_brand: isNew ? undefined : platform_brand,
          extend,
          ecommerce,
          repeat_brand,
          assignments: ecommerce ? assignments : null,
        });
        // if (platform === '喵站' && !extend) {
        //   hasExtend = false;
        // }
      });
      // if (!hasExtend) {
      //   message.warning('请选择猫站的站点');
      //   return;
      // }
      params.data = JSON.stringify(data);
      if (cm_id && !isCopy) {
        delete params.phone;
      }
      if (!isFieldsTouched() && changed) {
        params.onlyEcommerce = 1;
      }
      // const duplicates = findDuplicates(selectBrands);
      // if (duplicates.length > 0) {
      //   const msg = duplicates.map(v => originalBrands[v]).join('、');
      //   message.error(`${msg}所选品牌有重合，请修改`);
      //   return;
      // }
      if (isNew) {
        params.is_rules_new = 1;
      }
      if (isCopy) {
        params.cm_id = '';
      }
      fn(params);
      setSubmitting(true);
    });
  };

  const fn = params => {
    const _params = JSON.parse(JSON.stringify(params));
    let _data = [];
    try {
      _data = JSON.parse(_params.data);
    } catch (e) {
      _data = [];
    }
    if (_params.onlyEcommerce == 1) {
      delete _params.data;
    }
    addMixedIn({ ..._params, isZyAccount })
      .then(res => {
        const { code, msg, data: resData = {} } = res;
        setSubmitting(false);
        if (code == 0) {
          setId(resData.cm_id);
          message.success(msg);
          setSmsPhone({});
          onOk();
          if (resData.tip) {
            Modal.info({
              title: '温馨提示',
              content: resData.tip,
              okText: '我知道了',
            });
          }
          verifyModalRef.current.closeVerifyModal();
          if (isNew) {
            // ruleRef?.current?.refresh();
          } else {
            onCancel();
          }
        } else if (code == '19999' && resData.sms_code) {
          if (smsPhone?.phone) {
            message.info(msg);
          }
          const { username, password } = _data.find(v => v.platform == resData.sms_code) || {};
          setSmsPhone({
            phone: username,
            platform: resData.sms_code,
            password,
          });
        } else if (code == 1020) {
          Modal.confirm({
            title: '提示',
            content: msg,
            cancelText: '取消',
            okText: '确认',
            onCancel: () => {},
            onOk: () => {
              addMixedIn({ ..._params, isZyAccount, addUserConfirm: 1 });
            },
          });
        } else if (code == '19991') {
          const p = resData;
          if (params.cm_id) {
            p.cm_id = params.cm_id;
          } else {
            p.phone = params.phone;
          }
          verifyModalRef.current.handleOpen(p, { ..._params, isZyAccount });
        } else {
          message.error(msg);
        }
      })
      .catch(() => {
        setSubmitting(false);
      });
  };

  useEffect(
    () => {
      if (visible) {
        getMixInDetail({ cm_id, isZyAccount, isNew: isNew ? 1 : 0 }).then(res => {
          const { phone, phoneTrue, info, platform_brand = [], ecommerce } = res;
          // 如果是复制模式，将驿站账号置空
          if (isCopy) {
            setFieldsValue({
              phone: '', // 驿站账号置空
            });
            setParentPhone('');
          } else {
            setFieldsValue({
              phone,
            });
            setParentPhone(phoneTrue);
          }
          setModalConfig(info);
          setCommonBrands(platform_brand);
          // setOriginalBrands(brands);
          setRadioValue(+ecommerce || 0);
        });
      } else {
        setId(null);
        setModalConfig([]);
        resetFields();
      }
    },
    [cm_id, visible, isCopy, isNew],
  );

  const validator = (rule, value, callback) => {
    const { checked, username, password, platform_brand } = value;

    if (checked) {
      if (!username) {
        return callback('请输入对应的账号');
      }
      if (!password) {
        return callback('请输入对应的密码');
      }
      if (!isNew && platform_brand.length == 0) {
        return callback('请选择对应的品牌');
      }
    }
    return callback();
  };

  const handleChangeType = value => {
    setRadioValue(value);
    setChanged(true);
  };

  return (
    <Modal
      centered
      visible={visible}
      onOk={handleOk}
      onCancel={onCancel}
      confirmLoading={submitting}
      footer={isNew ? null : undefined}
      width="80%"
      destroyOnClose
    >
      <Form {...formItemLayout}>
        <FormItem label="驿站账号">
          {getFieldDecorator('phone', {
            rules: [
              {
                required: true,
                message: '请输入驿站手机号',
              },
            ],
          })(
            <Input
              disabled={!!cm_id && !isCopy}
              allowClear
              placeholder="请输入驿站手机号"
              style={{ width: 250 }}
              onChange={e => setParentPhone(e.target.value)}
            />,
          )}
        </FormItem>
        {isNew && (
          <Row type="flex" justify="space-between">
            <Col>
              <div style={{ fontWeight: 'bold', fontSize: '16px' }}>平台设置：</div>
            </Col>
            {!isCopy && (
              <Col>
                <HistoryModal cm_id={cm_id} isZyAccount={isZyAccount} />
              </Col>
            )}
          </Row>
        )}
        {modalConfig.map(item => (
          <FormItem key={item.platform} wrapperCol={{ span: 24 }}>
            {getFieldDecorator(item.platform, {
              initialValue: item,
              rules: [{ validator }],
            })(<MixInModalItem commonBrands={commonBrands} isNew={isNew} />)}
          </FormItem>
        ))}
        {isNew && (
          <Row type="flex" justify="center">
            <Button
              style={{ width: '124px' }}
              size="large"
              type="primary"
              onClick={handleOk}
              loading={submitting}
            >
              保存
            </Button>
          </Row>
        )}
      </Form>
      {isNew ? (
        <Rule
          ref={ruleRef}
          visible={visible}
          cm_id={cm_id}
          addedCmId={id}
          commonBrands={commonBrands}
          isZyAccount={isZyAccount}
          isCopy={isCopy}
          onSuccess={() => {
            onCancel();
            onOk();
          }}
        />
      ) : (
        <div>
          <p>同一品牌对应多个平台，且任务量均未满足时：</p>
          <Radio.Group value={radioValue} onChange={e => handleChangeType(e.target.value)}>
            <Radio style={{ display: 'block' }} value={0}>
              优先入快递平台
            </Radio>
            <Radio style={{ display: 'block' }} value={1}>
              优先入电商平台
            </Radio>
            <Radio style={{ display: 'block' }} value={2}>
              电商件入对应电商平台，其余入快递平台/kb
            </Radio>
            <Radio style={{ display: 'block' }} value={3}>
              电商件入对应电商平台，其余入dd
            </Radio>
            <Radio style={{ display: 'block' }} value={4}>
              电商件入对应电商平台，其余入cn
            </Radio>
          </Radio.Group>
        </div>
      )}

      <SmsModal
        submitting={submitting}
        phone={smsPhone.phone}
        platform={smsPhone.platform}
        password={smsPhone.password}
        onOk={handleOk}
        cm_id={cm_id}
        isCopy={isCopy}
        addedCmId={id}
        parentPhone={parentPhone}
        onCancel={() => {
          setSmsPhone({});
        }}
      />

      <VerifyModal ref={verifyModalRef} onSuccess={fn} />
    </Modal>
  );
};

export default Form.create()(MixInModal);
