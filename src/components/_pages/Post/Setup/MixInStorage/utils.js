/* eslint-disable no-plusplus */
/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getRuleDetail, setRuleDetail } from '@/services/setup';
import { changePostName } from '@/utils/changePostCompanyName';
import { message, Modal } from 'antd';
import { enquireScreen } from 'enquire-js';
import numeral from 'numeral';
import { useEffect, useImperativeHandle, useRef, useState } from 'react';

export function findDuplicates(array) {
  const set = new Set();
  const result = array.filter(item => {
    if (set.has(item)) {
      return true;
    }
    set.add(item);
    return false;
  });
  return [...new Set(result)];
}

export const useRules = (props, ref) => {
  const { cm_id, onSuccess, visible, isZyAccount, commonBrands, addedCmId, isCopy } = props;
  const [platform, setPlatform] = useState([]);
  const [rules, setRules] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const loadedRuleRef = useRef(false);

  useImperativeHandle(ref, () => ({
    refresh: getDetail,
  }));

  // const baseRuleBrands = ['sto', 'zt', 'yt', 'yd', 'jt', 'ems', 'yz', 'jd', 'dp', 'sf', 'dn'];
  // const ruleBrands = Array.isArray(commonBrands) ? commonBrands.map(v => v.value) : [];
  const numTypes = [
    { label: '全部类型', value: 'all' },
    { label: '拼多多单号', value: 'pdd' },
    { label: '淘系单号', value: 'cnys' },
    { label: '剩余单号', value: 'other' },
  ];

  const transformRules = (data = [], brands) => {
    const ruleBrands = Array.isArray(brands) ? brands.map(v => v.value) : [];
    return ruleBrands.map(item => {
      const currentBrandRule = data.find(v => v.brand_en === item);
      if (Array.isArray(currentBrandRule?.items) && currentBrandRule?.items?.length) {
        return {
          ...currentBrandRule,
          brand: item,
        };
      }
      return {
        brand: item,
        items: [{ type: 'all', items: [{ platform: undefined, num: undefined }] }],
      };
    });
  };

  const getDetail = async ({ id, isZy, brands, copy }) => {
    if (loadedRuleRef.current && copy) return;
    const { code, data } = await getRuleDetail({
      cm_id: id,
      isZyAccount: isZy,
    });
    if (code !== 0) return;
    const _platform = (Array.isArray(data?.complates) ? [...data.complates] : []).map(v => ({
      label: `入${v}`,
      value: v,
    }));
    const _rules = transformRules(Array.isArray(data?.rules) ? data.rules : [], brands);
    setPlatform(_platform);
    setRules(_rules);
    loadedRuleRef.current = true;
  };

  useEffect(
    () => {
      if (visible && commonBrands.length) {
        enquireScreen(b => {
          setIsMobile(b);
        });
        getDetail({
          id: cm_id || addedCmId,
          isZy: isZyAccount,
          brands: commonBrands,
          copy: isCopy,
        });
      }
    },
    [visible, commonBrands, cm_id, addedCmId, isZyAccount, isCopy],
  );

  const handleChange = (value, key, i, index, r_i) => {
    if (r_i != undefined && key === 'num' && value) {
      const c1 = value.match(/%/g);
      const c2 = value.match(/\./g);
      if (c1 && c2) return;
      if ((c1 && c1.length > 1) || (c2 && c2.length > 1)) return;
      if (!/^\d+$/.test(value) && value.indexOf('%') === -1 && value.indexOf('.') === -1) {
        return;
      }
      if (c1 && c1.length === 1) {
        if (value.indexOf('%') !== value.length - 1) return;
        const _value = value.replace('%', '');
        if (!/^\d+$/.test(_value)) return;
        value = (_value <= 1 ? 1 : _value > 100 ? 100 : _value) + '%';
      }
      if (c2 && c2.length === 1) {
        const _value = value.split('.');
        if (_value[0] > 1) return;
        if (_value[0] == 1) {
          value = '1';
        }
        if (_value[1].length > 2) {
          value = numeral(value).format('0.00');
        }
      }
    }
    const data = JSON.parse(JSON.stringify(rules));
    if (key === 'platform') {
      const items = data[i]?.items ?? [];
      let filterOptions = items[index]?.items ?? [];
      filterOptions = filterOptions
        .filter((_, _i) => _i != r_i)
        .filter(item => item.platform)
        .map(item => item.platform);
      if (filterOptions.includes(value)) {
        return message.warning('平级规则平台不允许重复');
      }
    }
    if (key === 'type') {
      if (index == 0 && value == 'other') {
        return message.warning('不允许选择剩余单号');
      }
      if (index != 0 && value == 'all') {
        return message.warning('不允许选择全部类型');
      }
    }
    if (r_i != undefined) {
      data[i].items[index].items[r_i][key] = value;
    } else {
      data[i].items[index][key] = value;
    }
    setRules(data);
  };

  const handleBlur = (i, index, r_i) => {
    const data = JSON.parse(JSON.stringify(rules));
    const items = rules[i]?.items[index]?.items ?? [];
    const { num } = items[r_i];
    if (num?.indexOf('.') > -1) {
      data[i].items[index].items[r_i].num = numeral(num).format('0%');
    }
    if (items.length > 1 && num && num.indexOf('%') === -1 && num.indexOf('.') === -1) {
      message.warning('请填写百分比任务量');
      data[i].items[index].items[r_i].num = undefined;
    }
    setRules(data);
  };
  const handleFocus = (i, index, r_i) => {
    const data = JSON.parse(JSON.stringify(rules));
    const items = rules[i]?.items[index]?.items ?? [];
    const { num } = items[r_i];
    if (num?.indexOf('%') > -1) {
      data[i].items[index].items[r_i].num = numeral(num).format('0.00');
    }
    setRules(data);
  };

  const add = (i, index, r_i) => {
    const data = JSON.parse(JSON.stringify(rules));
    if (r_i != undefined) {
      if (data[i].items[index].items.length === 10) {
        return message.warning('最多添加10条平级规则');
      }
      data[i].items[index].items.splice(r_i + 1, 0, { platform: undefined, num: undefined });
    } else {
      if (data[i].items.length === 10) {
        return message.warning('最多添加10条规则');
      }
      data[i].items.splice(index + 1, 0, {
        type: 'other',
        items: [{ platform: undefined, num: undefined }],
      });
    }
    setRules(data);
  };

  const minus = (i, index, r_i) => {
    const data = JSON.parse(JSON.stringify(rules));
    data[i].items[index].items.splice(r_i, 1);
    if (!data[i].items[index].items.length) {
      data[i].items.splice(index, 1);
    }
    if (!data[i].items.length) {
      return message.warning('至少保留一条规则');
    }
    setRules(data);
  };

  const serialize = d => {
    const data = JSON.parse(JSON.stringify(d));
    let err;
    for (let k = 0; k < data.length; k++) {
      const item = data[k].items;
      const brand = changePostName(data[k].brand) || data[k].brand_en || data[k].brand;
      for (let i = item.length - 1; i >= 0; i--) {
        const _item = item[i].items;
        let sum = 0;
        for (let j = _item.length - 1; j >= 0; j--) {
          const r = _item[j];
          if (!r.platform || (_item.length > 1 && !r.num)) {
            _item.splice(j, 1);
          } else if (r.num?.indexOf('.') > -1) {
            r.num = numeral(r.num).format('0%');
          }
          if (r.num?.indexOf('%') > -1) {
            sum += r.num.replace('%', '') * 1;
          }
        }
        if (sum > 100) {
          err = `${brand}的平级规则任务量必须小于100%`;
          break;
        }
        if (!_item.length) {
          item.splice(i, 1);
        }
        if (err) {
          break;
        }
      }
      if (err) {
        break;
      }
    }
    if (err) {
      message.error(err);
      throw new Error(err);
    }
    return data;
  };
  const submit = async () => {
    try {
      setLoading(true);
      const _rules = serialize(rules);
      if (!rules.length) {
        setLoading(false);
        onSuccess();
        return;
      }
      const { code, msg } = await setRuleDetail({
        cm_id: isCopy ? addedCmId : cm_id || addedCmId,
        rules: JSON.stringify(_rules),
        isZyAccount,
      });
      message.open({
        type: code === 0 ? 'success' : 'error',
        content: msg,
      });
      code === 0 && onSuccess();
      setLoading(false);
    } catch (error) {
      // eslint-disable-next-line no-console
      // console.log(error);
      setLoading(false);
    }
  };

  return {
    rules,
    platform,
    numTypes,
    handleChange,
    add,
    minus,
    submit,
    loading,
    handleBlur,
    handleFocus,
    isMobile,
  };
};

/**
 *
 * @param {*} userInfo
 */
export const checkSubAccount = (userInfo = {}) => {
  const { pid, phone, parent_phone } = userInfo;
  if (pid > 0) {
    Modal.info({
      title: '温馨提示',
      content: `${phone}为驿站子账号，对应主账号为${parent_phone}。之后请使用主账号搜索`,
      okText: '我知道了',
    });
  }
};
