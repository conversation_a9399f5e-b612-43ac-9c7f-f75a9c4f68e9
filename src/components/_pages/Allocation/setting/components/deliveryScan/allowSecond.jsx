/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { editDaopaiSwitch } from '@/services/allocation_setting';
import { Radio } from 'antd';
import React from 'react';

const AllowSecond = props => {
  const { type, keys, getSwitchReload } = props;

  const map = {
    8: {
      key: 'app_modify_repeat_pi',
      desc: '允许业务员在快递员APP上手动取消禁止',
    },
    16: {
      key: 'app_modify_record_repeat_check',
      desc: '允许业务员在快递员APP上手动取消提醒',
    },
  };

  const handleSet = () => {
    editDaopaiSwitch({
      type,
      switch: props[map[type].key] == '1' ? '0' : '1',
    }).then(res => {
      res && getSwitchReload && getSwitchReload();
    });
  };

  return (
    keys.some(key => props[key] == '1') && (
      <div>
        <Radio checked={Boolean(props[map[type].key] * 1)} onClick={handleSet}>
          {map[type].desc}
        </Radio>
      </div>
    )
  );
};

export default AllowSecond;
